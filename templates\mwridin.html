{% extends 'base.html' %}
{% block body %}
<div class="title">
    <p>قائمة الموردين</p>
    {% set vars = {'total': 0} %}
    {% for task in task %}
    {% if vars.update({'total' : vars.total|int + task.credit}) %} {% endif %}
    {% endfor %}
    <p>TOTAL = {{ vars.total }}</p>
</div>
<div class="form" style="padding-bottom: 1rem">
    <form action="/mwridin/" method="POST">
        <p class="form-title">إضافة مورد</p>
        <div class="first-line">
            <label>
                <span>الإسم :</span>
                <input type="text" required minlength="3" maxlength="25" pattern="^[a-zA-Z0-9]{3,25}$" name="name" />
            </label>
            <label>
                <span>الهاتف :</span>
                <input type="text" required minlength="10" pattern="^[0-9]*$" name="tele" />
            </label>
            <label>
                <span>الوصف :</span>
                <input type="text" maxlength="200" pattern="^[a-zA-Z0-9]{0,200}$" name="description" />
            </label>
            <label></label>
            <label></label>
            <label>
                <span style="visibility: hidden;">الهاتف :</span>
                <input value="إضافة المورد" type="submit" />
            </label>
        </div>
    </form>
</div>
<div class="mwridin-table" id="print">
    <input type="button" value="الطباعة" class="noprint" onclick="printDiv()"> 
    
    <table>
        <tr>
            <th>الإسم</th>
            <th>الهاتف</th>
            <th>الوصف</th>
            <th>المبلغ المأتمن</th>
            <th>العمليات</th> <!-- إضافة عمود العمليات -->
        </tr>

        {% for task in task %}
        <tr>
            <td>
                <a href="/factures/?clnt=1&clnt-id=lmmohamed">
                    {{ task.name }}
                </a>
            </td>
            <td>{{ task.tele }}</td>
            <td>{{ task.description }}</td>
            <td>{{ task.credit }} DH</td>
            <td>
                <form method='POST'>
                    <input type='hidden' value='{{ task.name }}' name="supplier" />
                    <input type='hidden' value='delete' name="action" />
                    <input type="submit" value='x'>
                </form>
            </td>
        </tr>
        {% endfor %}
    </table>
</div>
{% endblock %}
