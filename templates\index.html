{% extends 'home_base.html' %}
{% block body %}
            <!-- شريط التنقل العلوي -->
            <nav class="top-navbar">
                <div class="navbar-brand">
                    <h1>CALENDRIER AGRICOLE</h1>
                </div>
                <div class="navbar-menu">
                    <a href="/" class="nav-link active">الرئيسية</a>
                    <a href="/taqarir" class="nav-link">التقارير</a>
                    <a href="/factures" class="nav-link">الفواتير</a>
                </div>
            </nav>

            <h1 class="main-page__title">لوحة التحكم الرئيسية</h1>

            <!-- إحصائيات سريعة -->
            <div class="stats-container">
                {% set vars = {'total': 0|float, 'total2': 0|float, 'total3': 0|float} %}
                {% for task in task %}
                {% if vars.update({'total' : vars.total|float + task.total|float}) %} {% endif %}
                {% endfor %}
                {% for task2 in task2 %}
                {% if vars.update({'total2' : vars.total2|float + task2.credit|float}) %} {% endif %}
                {% endfor %}
                {% for task3 in task3 %}
                {% if vars.update({'total3' : vars.total3|float + task3.credit|float}) %} {% endif %}
                {% endfor %}

                <div class="stat-card">
                    <div class="stat-icon">📦</div>
                    <div class="stat-info">
                        <h3>المخزون</h3>
                        <p>{{ vars.total }} DH</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">🏪</div>
                    <div class="stat-info">
                        <h3>الموردون</h3>
                        <p>{{ vars.total2 }} DH</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">👥</div>
                    <div class="stat-info">
                        <h3>العملاء</h3>
                        <p>{{ vars.total3 }} DH</p>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">💰</div>
                    <div class="stat-info">
                        <h3>الخزينة</h3>
                        <p>{{ caise }} DH</p>
                    </div>
                </div>
            </div>

            <!-- لوحة التحكم الرئيسية -->
            <div class="dashboard-container">
                <h2 class="dashboard-title">لوحة التحكم</h2>

                <div class="dashboard-grid">
                    <a href="/makhzon" class="dashboard-card">
                        <div class="card-icon">📦</div>
                        <h3>المخزون</h3>
                        <p>إدارة المنتجات والكميات</p>
                    </a>

                    <a href="/mobaya3at" class="dashboard-card">
                        <div class="card-icon">💳</div>
                        <h3>فاتورة المبايعات</h3>
                        <p>إنشاء فواتير البيع</p>
                    </a>

                    <a href="/mochtarayat" class="dashboard-card">
                        <div class="card-icon">🛒</div>
                        <h3>فاتورة المشتريات</h3>
                        <p>إنشاء فواتير الشراء</p>
                    </a>

                    <a href="/3omalae" class="dashboard-card">
                        <div class="card-icon">👥</div>
                        <h3>قائمة العملاء</h3>
                        <p>إدارة بيانات العملاء</p>
                    </a>

                    <a href="/mwridin" class="dashboard-card">
                        <div class="card-icon">🏪</div>
                        <h3>قائمة الموردين</h3>
                        <p>إدارة بيانات الموردين</p>
                    </a>

                    <a href="/taqarir" class="dashboard-card">
                        <div class="card-icon">📊</div>
                        <h3>التقارير</h3>
                        <p>عرض التقارير والإحصائيات</p>
                    </a>

                    <a href="/factures" class="dashboard-card">
                        <div class="card-icon">📄</div>
                        <h3>الفواتير</h3>
                        <p>إدارة جميع الفواتير</p>
                    </a>

                    <a href="/stikhlass" class="dashboard-card">
                        <div class="card-icon">🧾</div>
                        <h3>ورقة الإستخلاص</h3>
                        <p>إنشاء إيصالات الدفع</p>
                    </a>

                    <a href="/taqrirsanawi" class="dashboard-card">
                        <div class="card-icon">📈</div>
                        <h3>التقرير السنوي</h3>
                        <p>التقارير السنوية المفصلة</p>
                    </a>

                    <a href="/devis" class="dashboard-card" target="_blank">
                        <div class="card-icon">💲</div>
                        <h3>التسعير</h3>
                        <p>إنشاء عروض الأسعار</p>
                    </a>
                </div>
            </div>

            <!-- نموذج إضافة مبلغ للخزينة -->
            <div class="cash-form-container">
                <h3>إضافة مبلغ للخزينة</h3>
                <form method="POST" class="cash-form">
                    <div class="cash-input-group">
                        <input type="number" placeholder="المبلغ" name="value" required>
                        <input type="submit" value="إضافة" />
                    </div>
                </form>
            </div>
{% endblock %}