from flask import Flask, render_template, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///database.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# Database Models
class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    quantity = db.Column(db.Float, default=0)
    price = db.Column(db.Float, default=0)
    total = db.Column(db.Float, default=0)
    date = db.Column(db.String(20), default=datetime.now().strftime('%Y-%m-%d'))

class Client(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    city = db.Column(db.String(100))
    tele = db.Column(db.String(20))
    description = db.Column(db.String(200))
    cin = db.Column(db.String(20))
    credit = db.Column(db.Float, default=0)

class Supplier(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    city = db.Column(db.String(100))
    tele = db.Column(db.String(20))
    description = db.Column(db.String(200))
    cin = db.Column(db.String(20))
    credit = db.Column(db.Float, default=0)

class Invoice(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    client = db.Column(db.String(100), nullable=False)
    total = db.Column(db.Float, default=0)
    typeVA = db.Column(db.String(1))  # V for Vente, A for Achat
    type_transaction = db.Column(db.String(20))
    date = db.Column(db.String(20), default=datetime.now().strftime('%Y-%m-%d'))

# Helper function to read/write cash
def get_cash():
    try:
        with open('caise.txt', 'r') as f:
            return float(f.read().strip())
    except:
        return 0.0

def set_cash(amount):
    with open('caise.txt', 'w') as f:
        f.write(str(amount))

# Routes
@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        value = float(request.form.get('value', 0))
        current_cash = get_cash()
        set_cash(current_cash + value)
        return redirect(url_for('index'))

    products = Product.query.all()
    suppliers = Supplier.query.all()
    clients = Client.query.all()
    cash = get_cash()

    return render_template('index.html',
                         task=products,
                         task2=suppliers,
                         task3=clients,
                         caise=cash)

@app.route('/makhzon', methods=['GET', 'POST'])
def makhzon():
    if request.method == 'POST':
        action = request.form.get('type')
        if action == 'add':
            name = request.form.get('name')
            quantity = float(request.form.get('quantity', 0))
            price = float(request.form.get('price', 0))
            total = quantity * price

            product = Product(name=name, quantity=quantity, price=price, total=total)
            db.session.add(product)
            db.session.commit()

        return redirect(url_for('makhzon'))

    products = Product.query.all()
    return render_template('makhzon.html', task=products)

@app.route('/3omalae', methods=['GET', 'POST'])
def clients():
    if request.method == 'POST':
        action = request.form.get('action')
        if action == 'add':
            name = request.form.get('name')
            city = request.form.get('city')
            tele = request.form.get('tele')
            description = request.form.get('description')
            cin = request.form.get('cin')

            client = Client(name=name, city=city, tele=tele, description=description, cin=cin)
            db.session.add(client)
            db.session.commit()
        elif action == 'delete':
            client_name = request.form.get('client')
            client = Client.query.filter_by(name=client_name).first()
            if client:
                db.session.delete(client)
                db.session.commit()

        return redirect(url_for('clients'))

    clients_list = Client.query.all()
    return render_template('3omalae.html', task=clients_list)

@app.route('/mwridin', methods=['GET', 'POST'])
def suppliers():
    if request.method == 'POST':
        action = request.form.get('action')
        if action == 'add':
            name = request.form.get('name')
            city = request.form.get('city')
            tele = request.form.get('tele')
            description = request.form.get('description')
            cin = request.form.get('cin')

            supplier = Supplier(name=name, city=city, tele=tele, description=description, cin=cin)
            db.session.add(supplier)
            db.session.commit()
        elif action == 'delete':
            supplier_name = request.form.get('supplier')
            supplier = Supplier.query.filter_by(name=supplier_name).first()
            if supplier:
                db.session.delete(supplier)
                db.session.commit()

        return redirect(url_for('suppliers'))

    suppliers_list = Supplier.query.all()
    return render_template('mwridin.html', task=suppliers_list)

@app.route('/mobaya3at')
def mobaya3at():
    products = Product.query.all()
    clients_list = Client.query.all()
    last_invoice = Invoice.query.order_by(Invoice.id.desc()).first()
    num = last_invoice.id if last_invoice else 0
    return render_template('mobaya3at.html', task=products, tasks=clients_list, num=num)

@app.route('/mochtarayat')
def mochtarayat():
    products = Product.query.all()
    suppliers_list = Supplier.query.all()
    return render_template('mochtarayat.html', task=products, tasks=suppliers_list)

@app.route('/genfacture/', methods=['POST'])
def genfacture():
    # Handle invoice generation
    client_name = request.form.get('id_client')
    total = float(request.form.get('total', 0))
    typeVA = request.form.get('typeVA')
    date = request.form.get('date')

    invoice = Invoice(client=client_name, total=total, typeVA=typeVA, date=date)
    db.session.add(invoice)
    db.session.commit()

    return redirect(url_for('index'))

@app.route('/taqarir')
def taqarir():
    invoices = Invoice.query.all()
    return render_template('taqarir.html', task=invoices)

@app.route('/factures')
def factures():
    invoices = Invoice.query.all()
    return render_template('factures.html', task=invoices)

@app.route('/stikhlass', methods=['GET', 'POST'])
def stikhlass():
    if request.method == 'POST':
        client_name = request.form.get('client')
        # Handle receipt generation logic here
        return render_template('stikhlassgen.html', client={'name': client_name},
                             date=datetime.now().strftime('%Y-%m-%d'), amount=0, check=0)

    clients_list = Client.query.all()
    suppliers_list = Supplier.query.all()
    return render_template('stikhlass.html', clients=clients_list, mowaridon=suppliers_list)

@app.route('/taqrirsanawi', methods=['GET', 'POST'])
def taqrirsanawi():
    if request.method == 'POST':
        client_name = request.form.get('client')
        year = request.form.get('year')
        # Handle annual report logic here
        client = Client.query.filter_by(name=client_name).first()
        return render_template('sanawi.html', client=client)

    clients_list = Client.query.all()
    suppliers_list = Supplier.query.all()
    return render_template('taqrirsanawi.html', clients=clients_list, mowaridon=suppliers_list)

@app.route('/devis')
def devis():
    return render_template('devis.html')

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)