@import url("https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap");@import url("https://fonts.googleapis.com/css2?family=Questrial&display=swap");*{font-size:100%;font-family:'Questrial','Almarai';margin:0;padding:0;box-sizing:border-box}:root{--main: #16a339;--secondary: #4cc956;--dark: #07471a;--dark2: #366941;--darkLowOp1: rgba(7, 71, 23, 0.3);--darkLowOp2: rgba(7, 71, 28, 0.2);--lightDark: rgba(185, 203, 211, 0.6);--light: #f3fdf7;--panel-box-shadow: -5px 0px 20px rgba(0, 0, 0, 0.1);--danger: #de1f43;--dangerDark: #8d0f26;--dangerDarker: #721325;--dangerText: #eee}body{width:100%;height:100vh;color:var(--dark);background: linear-gradient(135deg, var(--light) 0%, #f8fffe 50%, var(--light) 100%)}

/* خلفية خاصة للصفحة الرئيسية */
body.home-page {
    background: linear-gradient(135deg, #f3fdf7 0%, #e8f5e8 25%, #f0faf2 50%, #e6f7e6 75%, #f3fdf7 100%);
    min-height: 100vh;
}.wrapper{display:flex;align-items:center;width:100%;height:100%;overflow:hidden}.wrapper .content,.wrapper .side-panel{height:100%}.wrapper .content{position:relative;width:72%;order:2;overflow-x:hidden}

/* تصميم خاص للصفحة الرئيسية */
body.home-page .wrapper {
    display: block;
}

body.home-page .wrapper .content {
    width: 100%;
    order: unset;
}

body.home-page .wrapper .side-panel {
    display: none;
}

/* شريط التنقل العلوي */
.top-navbar {
    background: linear-gradient(135deg, var(--main), var(--secondary));
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.navbar-brand h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.navbar-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: background-color 0.3s ease;
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    background-color: rgba(255,255,255,0.2);
}

/* تعديل العنوان الرئيسي */
body.home-page .main-page__title {
    padding-top: 3rem;
    font-size: 3rem;
}.wrapper .content::after{position:absolute;z-index:-555;content:'';top:0;bottom:0;left:0;right:0;background:url("./LOGO.png");opacity:0.1;background-size:95%;background-position:center;background-repeat:no-repeat}.wrapper .content .main-page__title{padding-top:7rem;text-align:center;color:var(--main);font-weight:400;font-size:4rem}.wrapper .content .main-page__undertitle{padding-top:3rem;text-align:center;color:var(--dark);font-weight:500;font-size:1.5rem}.wrapper .content .error{text-align:center;color:var(--danger);font-weight:1.5rem}.wrapper .content .error.hidden{display:none}.wrapper .content .makhzon-table,.wrapper .content .homalae-table,.wrapper .content .mwridin-table,.wrapper .content .facture-table{width:95%;margin:1rem auto 3rem;height:fit-content}.wrapper .content .makhzon-table table,.wrapper .content .homalae-table table,.wrapper .content .mwridin-table table,.wrapper .content .facture-table table{width:100%;border-collapse:collapse;border-radius:10px 10px 0 0;overflow:hidden}.wrapper .content .makhzon-table table th,.wrapper .content .homalae-table table th,.wrapper .content .mwridin-table table th,.wrapper .content .facture-table table th{background-color:var(--dark);color:var(--light);padding:0.6rem 1rem;text-align:right}.wrapper .content .makhzon-table table tr,.wrapper .content .makhzon-table table th,.wrapper .content .makhzon-table table td,.wrapper .content .homalae-table table tr,.wrapper .content .homalae-table table th,.wrapper .content .homalae-table table td,.wrapper .content .mwridin-table table tr,.wrapper .content .mwridin-table table th,.wrapper .content .mwridin-table table td,.wrapper .content .facture-table table tr,.wrapper .content .facture-table table th,.wrapper .content .facture-table table td{font-size:1.1rem;word-wrap:break-word}.wrapper .content .makhzon-table table tr td:first-child,.wrapper .content .homalae-table table tr td:first-child,.wrapper .content .mwridin-table table tr td:first-child,.wrapper .content .facture-table table tr td:first-child{max-width:20rem}.wrapper .content .makhzon-table table tr:nth-child(odd),.wrapper .content .homalae-table table tr:nth-child(odd),.wrapper .content .mwridin-table table tr:nth-child(odd),.wrapper .content .facture-table table tr:nth-child(odd){background-color:var(--lightDark)}.wrapper .content .makhzon-table table td,.wrapper .content .homalae-table table td,.wrapper .content .mwridin-table table td,.wrapper .content .facture-table table td{padding:0.8rem 1rem}.wrapper .content .makhzon-table table td button,.wrapper .content .homalae-table table td button,.wrapper .content .mwridin-table table td button,.wrapper .content .facture-table table td button{background-color:var(--danger);transition:background-color 200ms ease;cursor:pointer;border:none;outline:none;padding:0.4rem 0.8rem;color:var(--dangerText);font-weight:600;border-radius:5px}.wrapper .content .makhzon-table table td button:hover,.wrapper .content .homalae-table table td button:hover,.wrapper .content .mwridin-table table td button:hover,.wrapper .content .facture-table table td button:hover{background-color:var(--dangerDark)}.wrapper .content .form{padding:2rem 2vw 3rem}.wrapper .content .form form .form-title{font-size:2rem;margin-bottom:2rem}.wrapper .content .form form.stikhlass{display:grid;grid-template-columns:repeat(auto-fit, 19rem);grid-template-rows:auto;grid-gap:2rem;justify-content:center;padding:0 2rem}.wrapper .content .form form.stikhlass label{width:19rem}.wrapper .content .form form.stikhlass label input,.wrapper .content .form form.stikhlass label select{width:13rem}.wrapper .content .form form .square{display:flex;flex-direction:column;align-items:center;justify-content:center}.wrapper .content .form form .square input[type="submit"]{margin-top:1.5rem}.wrapper .content .form form .square span{margin-left:1rem}.wrapper .content .form form .first-line{display:grid;width:100%;grid-template-columns:repeat(auto-fit, 20rem);grid-template-rows:auto;grid-gap:2rem;justify-content:center;position:relative;margin-bottom:2rem}.wrapper .content .form form .final-line{text-align:center;margin-top:1rem}.wrapper .content .form form .final-line .flex-container{display:flex;align-items:center;justify-content:center;margin-bottom:2rem}.wrapper .content .form form .final-line .flex-container .final-price{margin-left:4rem;font-size:1.2rem}.wrapper .content .form form .final-line .flex-container .payment-method span{margin-left:2rem}.wrapper .content .form form label{display:flex;align-items:center;justify-content:space-between}.wrapper .content .form form .asnaf{display:flex;flex-direction:column;min-width:100%}.wrapper .content .form form .asnaf-title{font-size:1.5rem;margin-top:1.5rem;margin-right:8vw}.wrapper .content .form form .asnaf .senf-labels{display:flex;align-items:center;justify-content:center}.wrapper .content .form form .asnaf .senf-labels label{display:flex;flex-direction:column;align-items:center;justify-content:space-between}.wrapper .content .form form .asnaf .senf-labels label span{margin-bottom:0.5rem}.wrapper .content .form form .asnaf .senf-labels label:not(:last-child){margin-left:1rem}.wrapper .content .form form .asnaf .senf-labels label input{margin-bottom:0.5rem}.wrapper .content .form form .asnaf .senf-labels label input[type="number"]{width:6rem}.wrapper .content .form form .asnaf input,.wrapper .content .form form .asnaf select{padding:0.4rem 0.5rem}.wrapper .content .form form input,.wrapper .content .form form select,.wrapper .content .form form textarea{padding:0.45rem 0.7rem;color:var(--dark);background-color:var(--light);border:2px solid var(--dark2);outline:none;border-radius:6px;width:14rem}.wrapper .content .form form input:focus,.wrapper .content .form form select:focus,.wrapper .content .form form textarea:focus{box-shadow:0px 0px 2px var(--darkLowOp1);border:2px solid var(--dark)}.wrapper .content .form form input[type="submit"]{border:none;background-color:var(--main);box-shadow:5px 5px 10px rgba(0,0,0,0.2);transition:background-color 200ms ease, box-shadow 300ms ease;color:var(--light);padding:1rem 2rem;cursor:pointer;border-radius:8px;width:auto;min-width:150px;height:60px;display:block;margin:1rem auto;font-size:1.1rem;font-weight:600}.wrapper .content .form form input[type="submit"]:hover{background-color:var(--secondary);box-shadow:0px 0px 0px rgba(0,0,0,0.2)}.wrapper .content .top-bar{position:absolute;top:0;left:0;right:0;padding:0.5rem;background-color:var(--danger);color:var(--dangerText);height:fit-content;text-align:center;transition:opacity 200ms ease}.wrapper .content .top-bar__close{position:absolute;top:-2px;left:1.8rem;cursor:pointer}.wrapper .content .top-bar__close:hover span{background-color:var(--dangerDarker)}.wrapper .content .top-bar__close span{display:block;height:1.2rem;width:4px;border-radius:10px;background-color:var(--dangerDark);transition:background-color 200ms ease;transform-origin:-11.5px}.wrapper .content .top-bar__close span:first-child{transform:rotate(45deg)}.wrapper .content .top-bar__close span:last-child{transform:rotate(-45deg)}.wrapper .content .title{width:100%;padding-top:2.5rem;padding-bottom:1rem;text-align:center;height:fit-content}.wrapper .content .title p{color:var(--main);font-weight:100;font-size:2.8rem}.wrapper .side-panel{width:28%;background-image:linear-gradient(-45deg, var(--main), var(--secondary));color:var(--light);box-shadow:var(--panel-box-shadow)}.wrapper .side-panel__logo{width:100%;padding:2rem 2.5rem;height:auto}.wrapper .side-panel__logo p{font-family:'Questrial';font-weight:700;font-size:2rem}.wrapper .side-panel__nav{width:100%}.wrapper .side-panel__nav ul{position:relative;width:100%;list-style:none}.wrapper .side-panel__nav ul li{font-size:1.2rem;width:100%;transition:background-color 200ms ease}.wrapper .side-panel__nav ul li:hover{background-color:var(--darkLowOp2)}.wrapper .side-panel__nav ul li div{padding:1rem}.wrapper .side-panel__nav ul li a{color:inherit;text-decoration:none}.wrapper .side-panel__nav ul li.active{background-color:var(--darkLowOp1);border-right:0.4rem solid var(--dark)}@media print{body{-webkit-print-color-adjust:exact}}body.facture-page{width:50rem;min-height:100vh;margin:0;padding:0;overflow:hidden;color:black}body.facture-page::after{position:absolute;z-index:-555;content:'';top:0;bottom:0;left:0;right:0;background:url("./LOGO.png");opacity:0.05;background-size:50rem;background-position:center;overflow:hidden;width:50rem}body.facture-page .print{position:fixed;width:50rem;z-index:10;height:100%}body.facture-page .content-wrapper{padding:2rem 2.5rem}body.facture-page .content-wrapper .logo{text-align:left}body.facture-page .content-wrapper .logo img{width:100%}body.facture-page .content-wrapper .horof{font-size:1.2rem;padding-top:0.3rem;padding-bottom:0.6rem;padding-right:17.7rem}body.facture-page .content-wrapper .horof b{letter-spacing:3px}body.facture-page .content-wrapper .fwtir{padding:0rem 2.4rem;font-size:1.1rem}body.facture-page .content-wrapper .signatures{padding:0rem 2.4rem 5rem;font-size:1.1rem}body.facture-page .content-wrapper .info{display:flex;align-items:flex-end;justify-content:space-around;width:100%;margin-top:1rem}body.facture-page .content-wrapper .info h3{font-size:1.5rem}body.facture-page .content-wrapper .info p{font-size:1.1rem;line-height:1.5rem}body.facture-page .content-wrapper .table{width:100%;margin:1rem 0 0}body.facture-page .content-wrapper .table table{width:100%}body.facture-page .content-wrapper .table tr,body.facture-page .content-wrapper .table td,body.facture-page .content-wrapper .table th{border:1px solid #616161;padding:0.3rem 0.2rem;text-align:center}body.facture-page .content-wrapper .table th{background-color:#ebebeb}body.facture-page .content-wrapper .ijmaly{width:14rem;text-align:right;margin:0.5rem 0 0rem;font-size:1.1rem}body.facture-page .content-wrapper .ijmaly table{width:100%}body.facture-page .content-wrapper .ijmaly table tr{width:100%}body.facture-page .content-wrapper .ijmaly table th{width:60%;margin-left:1rem}body.facture-page .content-wrapper .ijmaly table td{width:40%}body.facture-page .content-wrapper .footer{font-size:0.85rem;text-align:center;padding:1rem 1.5rem;background-color:var(--main);color:black;border-radius:10px;width:100%}body.facture-page .content-wrapper .footer p{line-height:1rem}

 a {
    color: inherit;
    text-decoration: inherit;
  }

  /* تنسيق خاص للأزرار المربعة في الوسط */
  .submit-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin: 2rem 0;
  }

  .submit-btn input[type="submit"] {
    border: none;
    background-color: var(--main);
    box-shadow: 5px 5px 10px rgba(0,0,0,0.2);
    transition: background-color 200ms ease, box-shadow 300ms ease;
    color: var(--light);
    padding: 1rem 2rem;
    cursor: pointer;
    border-radius: 8px;
    width: auto;
    min-width: 180px;
    height: 60px;
    font-size: 1.1rem;
    font-weight: 600;
  }

  .submit-btn input[type="submit"]:hover {
    background-color: var(--secondary);
    box-shadow: 0px 0px 0px rgba(0,0,0,0.2);
  }

  /* تنسيق خاص للأزرار داخل first-line */
  .wrapper .content .form form .first-line label:has(input[type="submit"]) {
    display: flex;
    justify-content: center;
    align-items: center;
    grid-column: 1 / -1;
  }

  .wrapper .content .form form .first-line input[type="submit"] {
    margin: 0 auto;
  }

  /* تنسيق إضافي للأزرار المربعة */
  .wrapper .content .form form input[type="submit"] {
    aspect-ratio: auto;
    min-height: 50px;
    border-radius: 8px !important;
    font-weight: 600 !important;
    text-align: center;
    display: inline-block;
  }

  /* تنسيق خاص للأزرار في الجداول */
  .wrapper .content .makhzon-table table td button,
  .wrapper .content .homalae-table table td button,
  .wrapper .content .mwridin-table table td button,
  .wrapper .content .facture-table table td button {
    border-radius: 6px !important;
    min-width: 40px;
    min-height: 35px;
  }

  @media print {
    .noprint {
       visibility: hidden;
    }
 }

/* تصميم الصفحة الرئيسية الجديد */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, var(--main), var(--secondary));
    color: white;
    padding: 2rem;
    border-radius: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 10px 30px rgba(22, 163, 57, 0.3);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
}

.stat-card:hover::before {
    animation: shimmer 1.5s ease-in-out;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 40px rgba(22, 163, 57, 0.4);
}

.stat-icon {
    font-size: 3rem;
    margin-left: 1rem;
    opacity: 0.9;
}

.stat-info h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.stat-info p {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
}

.dashboard-container {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.dashboard-title {
    text-align: center;
    color: var(--main);
    font-size: 2.5rem;
    margin-bottom: 2rem;
    font-weight: 600;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.dashboard-card {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border: 2px solid var(--lightDark);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    text-decoration: none;
    color: var(--dark);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(22, 163, 57, 0.1), transparent);
    transition: left 0.5s ease;
}

.dashboard-card:hover::before {
    left: 100%;
}

.dashboard-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 20px 40px rgba(22, 163, 57, 0.2);
    border-color: var(--main);
    background: linear-gradient(145deg, #ffffff, var(--light));
}

.card-icon {
    font-size: 4.5rem;
    margin-bottom: 1.2rem;
    display: block;
    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.1));
    transition: transform 0.3s ease;
}

.dashboard-card:hover .card-icon {
    transform: scale(1.1) rotate(5deg);
}

.dashboard-card h3 {
    font-size: 1.4rem;
    color: var(--main);
    margin-bottom: 0.8rem;
    font-weight: 700;
}

.dashboard-card p {
    color: var(--dark2);
    font-size: 1rem;
    line-height: 1.4;
    margin: 0;
}

.cash-form-container {
    background: linear-gradient(145deg, #ffffff, var(--light));
    border: 3px solid transparent;
    background-clip: padding-box;
    border-radius: 20px;
    padding: 2.5rem;
    margin: 3rem auto;
    max-width: 600px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(22, 163, 57, 0.15);
    position: relative;
}

.cash-form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 20px;
    padding: 3px;
    background: linear-gradient(135deg, var(--main), var(--secondary));
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    z-index: -1;
}

.cash-form-container h3 {
    color: var(--main);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.cash-input-group {
    display: flex;
    gap: 1rem;
    justify-content: center;
    align-items: center;
}

.cash-input-group input[type="number"] {
    padding: 0.8rem 1rem;
    border: 2px solid var(--dark2);
    border-radius: 8px;
    font-size: 1.1rem;
    width: 200px;
    text-align: center;
}

.cash-input-group input[type="submit"] {
    padding: 0.8rem 2rem;
    background: var(--main);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.cash-input-group input[type="submit"]:hover {
    background: var(--secondary);
}

/* تحسين التصميم للشاشات الصغيرة */
@media (max-width: 768px) {
    .top-navbar {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .navbar-menu {
        gap: 1rem;
    }

    .stats-container {
        grid-template-columns: 1fr;
        padding: 1rem;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
    }

    .dashboard-container {
        padding: 1rem;
    }

    .dashboard-title {
        font-size: 2rem;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .dashboard-card {
        padding: 1.5rem;
    }

    .card-icon {
        font-size: 3.5rem;
    }

    .cash-form-container {
        margin: 2rem 1rem;
        padding: 1.5rem;
    }

    .cash-input-group {
        flex-direction: column;
        gap: 1rem;
    }

    .cash-input-group input[type="number"] {
        width: 100%;
    }

    body.home-page .main-page__title {
        font-size: 2.2rem;
        padding-top: 2rem;
    }
}

@media (max-width: 480px) {
    .navbar-brand h1 {
        font-size: 1.4rem;
    }

    .nav-link {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }

    .dashboard-card h3 {
        font-size: 1.2rem;
    }

    .dashboard-card p {
        font-size: 0.9rem;
    }
}